# Copilot Instructions for Python Documentation

## Docstring Style

When generating docstrings for Python files in this project, please follow these rules:

1. Use Google Style docstrings format
2. Write the main description and explanatory text in Chinese
3. Keep structural keywords in English: Args, Returns, Raises, Yields, Examples, Note, Attributes, etc.

### Example

```python
def example_function(param1, param2=None, *args, **kwargs):
    """这个函数的简短描述，说明其功能和用途。
    
    这里是详细描述，可以包含多行文本，解释函数的工作原理、使用场景等。
    
    Args:
        param1 (int): 参数1的描述，说明其用途和影响
        param2 (str, optional): 参数2的描述，可选参数
        *args: 可变位置参数的描述
        **kwargs: 可变关键字参数的描述
    
    Returns:
        bool: 返回值的描述，说明返回结果的含义
        
    Raises:
        ValueError: 说明在什么情况下会抛出此异常
        
    Examples:
        >>> example_function(1, "test")
        True
    """
    # 函数实现...
    return True
```

## Implementation Notes

- 这些规则仅适用于Python文件
- 函数内的注释也应该使用中文
- 变量名和函数名保持原有的命名风格，不要翻译
```
